{{--
    Reusable Confirmation Modal Component

    This component provides a standardized confirmation modal for various actions
    across the admin interface with consistent styling and behavior.

    Parameters:
    - $modalId: Unique ID for the modal (default: 'confirmationModal')
    - $type: Modal type for color theming (default: 'danger') - Options: 'success', 'info', 'warning', 'danger'
    - $icon: Icon class for the header (default: auto-selected based on type)
    - $modalTitle: Title text displayed next to the icon (default: auto-generated based on type)
    - $title: Main confirmation text (required)
    - $warningText: Warning message text (default: 'This action cannot be undone!')
    - $cancelText: Cancel button text (default: 'No, Cancel')
    - $confirmText: Confirm button text (default: 'Yes, Confirm')
    - $dismissText: Dismiss button text for notification mode (optional)
    - $formAction: Form action URL for the confirmation (required)
    - $formMethod: HTTP method for the form (default: 'DELETE')
    - $targetElementId: ID of the element to update with dynamic content (optional)

    Usage Examples:

    Basic Usage (Danger - Default):
    <x-confirmation-modal
        modal-id="deleteUserModal"
        title="Are you sure you want to delete this user?"
        form-action="{{ route('admin.users.destroy', $user) }}"
        confirm-text="Yes, Delete User"
    />

    Success Modal:
    <x-confirmation-modal
        modal-id="approveModal"
        type="success"
        title="Are you sure you want to approve this request?"
        warning-text="This will activate the user account."
        confirm-text="Yes, Approve"
        form-action="{{ route('admin.users.approve', $user) }}"
        form-method="PATCH"
    />

    Warning Modal:
    <x-confirmation-modal
        modal-id="archiveModal"
        type="warning"
        title="Are you sure you want to archive this item?"
        warning-text="This item will be moved to the archive."
        cancel-text="Keep Active"
        confirm-text="Yes, Archive"
        form-action="{{ route('admin.items.archive', $item) }}"
        form-method="PATCH"
    />

    Info Modal:
    <x-confirmation-modal
        modal-id="notifyModal"
        type="info"
        title="Send notification to all users?"
        warning-text="This will send an email to all registered users."
        confirm-text="Yes, Send"
        form-action="{{ route('admin.notifications.send') }}"
        form-method="POST"
    />

    Notification Modal (Single Button):
    <x-confirmation-modal
        modal-id="infoNotificationModal"
        type="info"
        title="Operation completed successfully!"
        warning-text="Your request has been processed."
        dismiss-text="OK"
        form-action="#"
    />

    With Dynamic Content:
    <x-confirmation-modal
        modal-id="deleteUtilityModal"
        title="Are you sure you want to delete the utility &quot;<span id='utilityName' class='fw-semibold'></span>&quot;?"
        form-action="#"
        confirm-text="Yes, Delete Utility"
        target-element-id="utilityName"
    />
--}}

@php
    // Define modal type configurations
    $typeConfig = [
        'success' => [
            'icon' => 'ri-check-fill',
            'title' => 'Confirm Action',
            'colorClass' => 'success'
        ],
        'info' => [
            'icon' => 'ri-information-fill',
            'title' => 'Information',
            'colorClass' => 'info'
        ],
        'warning' => [
            'icon' => 'ri-alert-fill',
            'title' => 'Warning',
            'colorClass' => 'warning'
        ],
        'danger' => [
            'icon' => 'ri-error-warning-fill',
            'title' => 'Confirm Deletion',
            'colorClass' => 'danger'
        ]
    ];

    // Set default type if not provided or invalid
    $modalType = in_array($type ?? 'danger', array_keys($typeConfig)) ? ($type ?? 'danger') : 'danger';
    $config = $typeConfig[$modalType];
@endphp

@props([
    'modalId' => 'confirmationModal',
    'type' => 'danger',
    'icon' => $config['icon'],
    'modalTitle' => $config['title'],
    'title' => '',
    'warningText' => 'This action cannot be undone!',
    'cancelText' => 'No, Cancel',
    'confirmText' => 'Yes, Confirm',
    'dismissText' => '',
    'formAction' => '#',
    'formMethod' => 'DELETE',
    'targetElementId' => null
])

<!-- Confirmation Modal -->
<div class="modal fade effect-scale" id="{{ $modalId }}" tabindex="-1" aria-labelledby="{{ $modalId }}Label" aria-hidden="true" style="display: none !important;">
    <div class="modal-dialog modal-dialog-centered" style="max-width: 550px;">
        <div class="modal-content">
            <!-- Header with Icon and Title -->
            <div class="modal-header border-0 pb-2">
                <div class="d-flex align-items-center">
                    <i class="{{ $icon }} text-{{ $config['colorClass'] }} me-2" style="font-size: 1.4rem;"></i>
                    <h5 class="modal-title fw-bold mb-0" style="font-size: 1.1rem;">{{ $modalTitle }}</h5>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>

            <!-- Modal Body -->
            <div class="modal-body pt-0">
                <p class="fs-6 mb-3" style="margin-left: 2.1rem;">{!! $title !!}</p>
                <p class="text-{{ $config['colorClass'] }} fw-semibold mb-0" style="margin-left: 2.1rem;">{{ $warningText }}</p>
            </div>

            <!-- Modal Footer with Horizontal Separator -->
            <hr class="my-0">
            <div class="modal-footer border-0 {{ !empty($dismissText) || empty($confirmText) ? 'justify-content-center' : 'justify-content-between' }}">
                @if(!empty($dismissText))
                    <!-- Notification modal with single dismiss button -->
                    <button type="button" class="btn btn-{{ $config['colorClass'] }}" data-bs-dismiss="modal">{{ $dismissText }}</button>
                @elseif(empty($confirmText))
                    <!-- Information modal with only close button -->
                    <button type="button" class="btn btn-outline-{{ $config['colorClass'] }}" data-bs-dismiss="modal">{{ $cancelText }}</button>
                @else
                    <!-- Confirmation modal with both buttons -->
                    <button type="button" class="btn btn-outline-dark" style="margin-left: 2.0rem;" data-bs-dismiss="modal">{{ $cancelText }}</button>
                    <form id="{{ $modalId }}Form" method="POST" action="{{ $formAction }}" style="display: inline;">
                        @csrf
                        @if(strtoupper($formMethod) !== 'POST')
                            @method($formMethod)
                        @endif
                        <button type="submit" class="btn btn-{{ $config['colorClass'] }}" style="margin-right: 2.0rem;">{{ $confirmText }}</button>
                    </form>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Anti-FOUC Styles for Confirmation Modals -->
<style>
/* Prevent Flash of Unstyled Content (FOUC) for confirmation modals */
.modal.fade.effect-scale {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
}

/* Ensure modal is properly hidden during page load */
.modal.fade.effect-scale:not(.show) {
    display: none !important;
}

/* Only show modal when Bootstrap adds the show class */
.modal.fade.effect-scale.show {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Ensure modal dialog has proper initial state for effect-scale animation */
.modal.fade.effect-scale .modal-dialog {
    transform: scale(0.7);
    opacity: 0;
    transition: all 0.3s ease;
}

/* Animate modal dialog when shown */
.modal.fade.effect-scale.show .modal-dialog {
    transform: scale(1);
    opacity: 1;
}

/* Additional safeguard for any confirmation modal during page load */
[id*="Modal"].modal:not(.show),
[id*="modal"].modal:not(.show) {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
}
</style>
