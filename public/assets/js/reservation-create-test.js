/**
 * Reservation Create Page Test Script
 * 
 * This script provides testing utilities to verify the FOUC fix is working correctly
 * and the page loads smoothly without white screen flashes.
 * 
 * Author: SMP Online Development Team
 * Version: 1.0.0
 * Last Updated: 2025-08-29
 */

(function() {
    'use strict';

    // Test configuration
    const testConfig = {
        enableLogging: true,
        measurePerformance: true,
        checkLoadingStates: true
    };

    /**
     * Log test messages
     */
    function testLog(message, data = null) {
        if (testConfig.enableLogging && window.console && console.log) {
            console.log('[Reservation Create Test]', message, data || '');
        }
    }

    /**
     * Test loading state management
     */
    function testLoadingStates() {
        testLog('Testing loading states...');

        const overlay = document.getElementById('contentLoadingOverlay');
        const formContent = document.getElementById('formContent');
        const container = document.getElementById('reservationFormContainer');

        // Check initial states
        const initialStates = {
            overlayVisible: overlay && !overlay.classList.contains('hidden'),
            formContentLoaded: formContent && formContent.classList.contains('loaded'),
            containerLoading: container && container.classList.contains('loading')
        };

        testLog('Initial loading states:', initialStates);

        // Monitor state changes
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    const target = mutation.target;
                    if (target.id === 'contentLoadingOverlay') {
                        testLog('Content loading overlay state changed:', {
                            hidden: target.classList.contains('hidden'),
                            display: target.style.display
                        });
                    } else if (target.id === 'formContent') {
                        testLog('Form content state changed:', {
                            loaded: target.classList.contains('loaded'),
                            visible: target.style.visibility,
                            opacity: target.style.opacity
                        });
                    }
                }
            });
        });

        // Observe changes
        if (overlay) observer.observe(overlay, { attributes: true });
        if (formContent) observer.observe(formContent, { attributes: true });
        if (container) observer.observe(container, { attributes: true });

        // Stop observing after 5 seconds
        setTimeout(() => {
            observer.disconnect();
            testLog('Stopped monitoring loading states');
        }, 5000);
    }

    /**
     * Test performance metrics
     */
    function testPerformance() {
        if (!testConfig.measurePerformance || !window.performance) {
            return;
        }

        testLog('Testing performance metrics...');

        // Measure page load time
        window.addEventListener('load', function() {
            const loadTime = performance.now();
            testLog('Page load time:', loadTime.toFixed(2) + 'ms');

            // Measure specific timings
            const navigation = performance.getEntriesByType('navigation')[0];
            if (navigation) {
                testLog('Navigation timing:', {
                    domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
                    loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
                    totalTime: navigation.loadEventEnd - navigation.fetchStart
                });
            }
        });

        // Monitor resource loading
        const resourceObserver = new PerformanceObserver(function(list) {
            list.getEntries().forEach(function(entry) {
                if (entry.name.includes('reservation-create')) {
                    testLog('Resource loaded:', {
                        name: entry.name,
                        duration: entry.duration.toFixed(2) + 'ms',
                        size: entry.transferSize || 'unknown'
                    });
                }
            });
        });

        resourceObserver.observe({ entryTypes: ['resource'] });
    }

    /**
     * Test AJAX functionality
     */
    function testAjaxFunctionality() {
        testLog('Testing AJAX functionality...');

        // Monitor fetch requests
        const originalFetch = window.fetch;
        let requestCount = 0;

        window.fetch = function(...args) {
            requestCount++;
            const url = args[0];
            testLog('AJAX request #' + requestCount + ':', url);

            const startTime = performance.now();
            return originalFetch.apply(this, args)
                .then(response => {
                    const endTime = performance.now();
                    testLog('AJAX response #' + requestCount + ':', {
                        url: url,
                        status: response.status,
                        duration: (endTime - startTime).toFixed(2) + 'ms'
                    });
                    return response;
                })
                .catch(error => {
                    const endTime = performance.now();
                    testLog('AJAX error #' + requestCount + ':', {
                        url: url,
                        error: error.message,
                        duration: (endTime - startTime).toFixed(2) + 'ms'
                    });
                    throw error;
                });
        };
    }

    /**
     * Test form functionality
     */
    function testFormFunctionality() {
        testLog('Testing form functionality...');

        // Test field selection
        const fieldSelect = document.getElementById('field_id');
        if (fieldSelect) {
            fieldSelect.addEventListener('change', function() {
                testLog('Field selection changed:', this.value);
            });
        }

        // Test date selection
        const dateInput = document.getElementById('booking_date');
        if (dateInput) {
            dateInput.addEventListener('change', function() {
                testLog('Date selection changed:', this.value);
            });
        }

        // Test time selections
        const startTimeSelect = document.getElementById('start_time');
        const endTimeSelect = document.getElementById('end_time');
        
        if (startTimeSelect) {
            startTimeSelect.addEventListener('change', function() {
                testLog('Start time changed:', this.value);
            });
        }

        if (endTimeSelect) {
            endTimeSelect.addEventListener('change', function() {
                testLog('End time changed:', this.value);
            });
        }
    }

    /**
     * Run all tests
     */
    function runTests() {
        testLog('Starting reservation create page tests...');

        if (testConfig.checkLoadingStates) {
            testLoadingStates();
        }

        if (testConfig.measurePerformance) {
            testPerformance();
        }

        testAjaxFunctionality();
        testFormFunctionality();

        testLog('All tests initialized');
    }

    /**
     * Initialize tests when DOM is ready
     */
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', runTests);
    } else {
        runTests();
    }

    // Export test functions for manual testing
    window.ReservationCreateTest = {
        testLoadingStates,
        testPerformance,
        testAjaxFunctionality,
        testFormFunctionality,
        runTests,
        config: testConfig
    };

})();
