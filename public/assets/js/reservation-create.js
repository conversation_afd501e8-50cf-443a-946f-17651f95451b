/**
 * Reservation Create Page JavaScript
 * 
 * This file contains all the JavaScript functionality for the reservation create page
 * to improve page loading performance by moving heavy scripts out of inline code.
 * 
 * Author: SMP Online Development Team
 * Version: 1.0.0
 * Last Updated: 2025-08-29
 */

// Global variables
let utilities = [];
let costCalculationTimeout;
let lastRequestData = null;
let currentRequest = null;

/**
 * Content Area Loading Management
 */
function showPageContent() {
    const overlay = document.getElementById('contentLoadingOverlay');
    const formContent = document.getElementById('formContent');
    const container = document.getElementById('reservationFormContainer');

    // Hide content loading overlay
    if (overlay) {
        overlay.classList.add('hidden');
        setTimeout(() => {
            overlay.style.display = 'none';
        }, 300);
    }

    // Show form content
    if (formContent) {
        formContent.classList.add('loaded');
    }

    // Remove loading class from container
    if (container) {
        container.classList.remove('loading');
    }
}

/**
 * Initialize page with proper loading states
 */
function initializeReservationForm() {
    // Show loading state initially
    const container = document.getElementById('reservationFormContainer');
    if (container) {
        container.classList.add('loading');
    }

    // Initialize form functions
    if (typeof updateFieldInfo === 'function') updateFieldInfo();
    if (typeof enableProgressiveFields === 'function') enableProgressiveFields();
    if (typeof checkUtilityPrerequisites === 'function') checkUtilityPrerequisites();

    // Load availability asynchronously
    const fieldId = document.getElementById('field_id')?.value;
    const date = document.getElementById('booking_date')?.value;
    
    if (fieldId && date && typeof loadAvailability === 'function') {
        // If we have initial data, load availability
        loadAvailability().then(() => {
            // Show content after availability is loaded
            setTimeout(showPageContent, 100);
        }).catch(() => {
            // Show content even if availability fails
            setTimeout(showPageContent, 100);
        });
    } else {
        // No initial data, show content immediately
        setTimeout(showPageContent, 100);
    }
}

/**
 * Optimized AJAX request with caching and debouncing
 */
function makeOptimizedRequest(url, data, options = {}) {
    const {
        cache = true,
        debounceMs = 300,
        timeout = 10000
    } = options;

    return new Promise((resolve, reject) => {
        // Debounce if specified
        if (debounceMs > 0) {
            if (costCalculationTimeout) {
                clearTimeout(costCalculationTimeout);
            }
            
            costCalculationTimeout = setTimeout(() => {
                executeRequest();
            }, debounceMs);
        } else {
            executeRequest();
        }

        function executeRequest() {
            // Cancel previous request if still pending
            if (currentRequest) {
                currentRequest.abort();
            }

            // Check cache if enabled
            const requestKey = JSON.stringify({ url, data });
            if (cache && lastRequestData === requestKey) {
                return;
            }

            const controller = new AbortController();
            currentRequest = controller;

            // Set timeout
            const timeoutId = setTimeout(() => {
                controller.abort();
                reject(new Error('Request timeout'));
            }, timeout);

            fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                },
                body: JSON.stringify(data),
                signal: controller.signal
            })
            .then(response => {
                clearTimeout(timeoutId);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(result => {
                lastRequestData = requestKey;
                currentRequest = null;
                resolve(result);
            })
            .catch(error => {
                clearTimeout(timeoutId);
                currentRequest = null;
                if (error.name !== 'AbortError') {
                    reject(error);
                }
            });
        }
    });
}

/**
 * Enhanced error handling for AJAX requests
 */
function handleAjaxError(error, context = 'Request') {
    console.error(`${context} error:`, error);

    // Show user-friendly error message
    const errorMessage = error.message || 'An unexpected error occurred';

    // Since JavaScript is required for this page to function,
    // we just log the error for debugging purposes
}

/**
 * Performance monitoring
 */
function measurePerformance(label, fn) {
    const start = performance.now();
    const result = fn();
    const end = performance.now();
    
    if (window.console && console.log) {
        console.log(`${label} took ${(end - start).toFixed(2)} milliseconds`);
    }
    
    return result;
}

/**
 * Lazy loading for heavy operations
 */
function lazyLoadFeature(featureName, loadFn) {
    return function(...args) {
        if (!window[`${featureName}Loaded`]) {
            window[`${featureName}Loaded`] = true;
            loadFn();
        }
        
        // Execute the original function if it exists
        const originalFn = window[featureName];
        if (typeof originalFn === 'function') {
            return originalFn.apply(this, args);
        }
    };
}

/**
 * DOM ready handler with fallback
 */
function onDOMReady(callback) {
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', callback);
    } else {
        // DOM is already ready
        callback();
    }
}

/**
 * Initialize everything when DOM is ready
 */
onDOMReady(function() {
    // Initialize the reservation form
    initializeReservationForm();
    
    // Set up global error handling
    window.addEventListener('unhandledrejection', function(event) {
        handleAjaxError(event.reason, 'Unhandled Promise');
    });
    
    // Performance monitoring for page load
    if (window.performance && performance.mark) {
        performance.mark('reservation-form-init-start');
        
        setTimeout(() => {
            performance.mark('reservation-form-init-end');
            performance.measure('reservation-form-init', 'reservation-form-init-start', 'reservation-form-init-end');
        }, 1000);
    }
});

// Export functions for global access
window.ReservationForm = {
    showPageContent,
    initializeReservationForm,
    makeOptimizedRequest,
    handleAjaxError,
    measurePerformance,
    lazyLoadFeature
};
