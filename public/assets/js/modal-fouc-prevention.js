/**
 * Modal FOUC (Flash of Unstyled Content) Prevention JavaScript
 * 
 * This script provides additional JavaScript-based protection against modal FOUC
 * by ensuring modals remain hidden until properly initialized and only show
 * when explicitly triggered.
 * 
 * Author: SMP Online Development Team
 * Version: 1.0.0
 * Last Updated: 2025-08-29
 */

(function() {
    'use strict';

    /**
     * Configuration object for modal FOUC prevention
     */
    const config = {
        // Selectors for different types of modals
        selectors: {
            allModals: '.modal',
            confirmationModals: '[id*="Modal"], [id*="modal"], [id*="confirmation"]',
            effectScaleModals: '.modal.fade.effect-scale'
        },
        
        // CSS classes for hiding/showing modals
        classes: {
            hidden: 'modal-fouc-hidden',
            visible: 'modal-fouc-visible'
        },
        
        // Debug mode (set to true for console logging)
        debug: false
    };

    /**
     * Utility function for debug logging
     */
    function debugLog(message, data = null) {
        if (config.debug) {
            console.log('[Modal FOUC Prevention]', message, data || '');
        }
    }

    /**
     * Force hide all modals immediately on page load
     * This provides an additional layer of protection beyond CSS
     */
    function forceHideModals() {
        debugLog('Force hiding all modals on page load');
        
        const modals = document.querySelectorAll(config.selectors.allModals);
        
        modals.forEach(modal => {
            // Apply multiple hiding methods for maximum compatibility
            modal.style.display = 'none';
            modal.style.opacity = '0';
            modal.style.visibility = 'hidden';
            modal.classList.add(config.classes.hidden);
            modal.classList.remove(config.classes.visible);
            
            // Ensure aria-hidden is set correctly
            modal.setAttribute('aria-hidden', 'true');
            
            debugLog('Hidden modal:', modal.id || modal.className);
        });
    }

    /**
     * Initialize modal event listeners to handle show/hide events
     */
    function initializeModalEventListeners() {
        debugLog('Initializing modal event listeners');
        
        const modals = document.querySelectorAll(config.selectors.allModals);
        
        modals.forEach(modal => {
            // Listen for Bootstrap modal show events
            modal.addEventListener('show.bs.modal', function(event) {
                debugLog('Modal showing:', this.id || this.className);
                
                // Remove FOUC prevention classes when modal is being shown
                this.classList.remove(config.classes.hidden);
                this.classList.add(config.classes.visible);
                this.style.display = '';
                this.style.opacity = '';
                this.style.visibility = '';
            });
            
            // Listen for Bootstrap modal hide events
            modal.addEventListener('hide.bs.modal', function(event) {
                debugLog('Modal hiding:', this.id || this.className);
            });
            
            // Listen for Bootstrap modal hidden events
            modal.addEventListener('hidden.bs.modal', function(event) {
                debugLog('Modal hidden:', this.id || this.className);
                
                // Re-apply FOUC prevention when modal is fully hidden
                this.classList.add(config.classes.hidden);
                this.classList.remove(config.classes.visible);
                this.style.display = 'none';
                this.style.opacity = '0';
                this.style.visibility = 'hidden';
            });
        });
    }

    /**
     * Handle effect-scale modals specifically
     */
    function handleEffectScaleModals() {
        debugLog('Handling effect-scale modals');
        
        const effectScaleModals = document.querySelectorAll(config.selectors.effectScaleModals);
        
        effectScaleModals.forEach(modal => {
            const modalDialog = modal.querySelector('.modal-dialog');
            
            if (modalDialog) {
                // Ensure initial transform state
                modalDialog.style.transform = 'scale(0.7)';
                modalDialog.style.opacity = '0';
                
                // Listen for show event to animate properly
                modal.addEventListener('show.bs.modal', function() {
                    setTimeout(() => {
                        if (modalDialog) {
                            modalDialog.style.transform = 'scale(1)';
                            modalDialog.style.opacity = '1';
                        }
                    }, 10); // Small delay to ensure CSS transition works
                });
                
                // Reset transform on hide
                modal.addEventListener('hidden.bs.modal', function() {
                    if (modalDialog) {
                        modalDialog.style.transform = 'scale(0.7)';
                        modalDialog.style.opacity = '0';
                    }
                });
            }
        });
    }

    /**
     * Monitor for dynamically added modals
     */
    function observeModalChanges() {
        if (typeof MutationObserver !== 'undefined') {
            debugLog('Setting up MutationObserver for dynamic modals');
            
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1) { // Element node
                            // Check if the added node is a modal
                            if (node.classList && node.classList.contains('modal')) {
                                debugLog('New modal detected:', node.id || node.className);
                                forceHideModal(node);
                                initializeModalEventListener(node);
                            }
                            
                            // Check if the added node contains modals
                            const modals = node.querySelectorAll && node.querySelectorAll(config.selectors.allModals);
                            if (modals && modals.length > 0) {
                                modals.forEach(modal => {
                                    debugLog('New modal in added content:', modal.id || modal.className);
                                    forceHideModal(modal);
                                    initializeModalEventListener(modal);
                                });
                            }
                        }
                    });
                });
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }
    }

    /**
     * Force hide a single modal
     */
    function forceHideModal(modal) {
        modal.style.display = 'none';
        modal.style.opacity = '0';
        modal.style.visibility = 'hidden';
        modal.classList.add(config.classes.hidden);
        modal.classList.remove(config.classes.visible);
        modal.setAttribute('aria-hidden', 'true');
    }

    /**
     * Initialize event listener for a single modal
     */
    function initializeModalEventListener(modal) {
        // Prevent duplicate listeners
        if (modal.hasAttribute('data-fouc-initialized')) {
            return;
        }
        
        modal.setAttribute('data-fouc-initialized', 'true');
        
        modal.addEventListener('show.bs.modal', function(event) {
            this.classList.remove(config.classes.hidden);
            this.classList.add(config.classes.visible);
            this.style.display = '';
            this.style.opacity = '';
            this.style.visibility = '';
        });
        
        modal.addEventListener('hidden.bs.modal', function(event) {
            this.classList.add(config.classes.hidden);
            this.classList.remove(config.classes.visible);
            this.style.display = 'none';
            this.style.opacity = '0';
            this.style.visibility = 'hidden';
        });
    }

    /**
     * Initialize all FOUC prevention measures
     */
    function init() {
        debugLog('Initializing Modal FOUC Prevention');
        
        // Force hide modals immediately
        forceHideModals();
        
        // Set up event listeners
        initializeModalEventListeners();
        
        // Handle effect-scale modals
        handleEffectScaleModals();
        
        // Monitor for dynamic modals
        observeModalChanges();
        
        debugLog('Modal FOUC Prevention initialized successfully');
    }

    /**
     * Run initialization when DOM is ready
     */
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    /**
     * Also run on window load as a fallback
     */
    window.addEventListener('load', function() {
        // Re-run force hide in case any modals appeared during load
        forceHideModals();
    });

    /**
     * Expose configuration for debugging
     */
    if (config.debug) {
        window.modalFoucPrevention = {
            config: config,
            forceHideModals: forceHideModals,
            init: init
        };
    }

})();
