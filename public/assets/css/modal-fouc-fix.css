/**
 * Modal FOUC (Flash of Unstyled Content) Prevention
 * 
 * This CSS file prevents modals from briefly appearing unstyled during page load
 * by ensuring they remain completely hidden until Bootstrap JavaScript initializes
 * and explicitly shows them.
 * 
 * Author: SMP Online Development Team
 * Version: 1.0.0
 * Last Updated: 2025-08-29
 */

/* ==========================================================================
   Core Modal FOUC Prevention
   ========================================================================== */

/**
 * Ensure all Bootstrap modals are completely hidden during page load
 * This prevents any flash of content before CSS and JS fully load
 */
.modal {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
}

/**
 * Only show modals when <PERSON><PERSON><PERSON> explicitly adds the 'show' class
 * This ensures modals only appear when intentionally triggered
 */
.modal.show {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* ==========================================================================
   Effect-Scale Animation FOUC Prevention
   ========================================================================== */

/**
 * Specific handling for modals with effect-scale animation
 * Ensures proper initial state and smooth transitions
 */
.modal.fade.effect-scale {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
}

.modal.fade.effect-scale:not(.show) {
    display: none !important;
}

.modal.fade.effect-scale.show {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/**
 * Modal dialog animation states for effect-scale
 */
.modal.fade.effect-scale .modal-dialog {
    transform: scale(0.7);
    opacity: 0;
    transition: all 0.3s ease;
}

.modal.fade.effect-scale.show .modal-dialog {
    transform: scale(1);
    opacity: 1;
}

/* ==========================================================================
   Confirmation Modal Specific FOUC Prevention
   ========================================================================== */

/**
 * Additional safeguards for confirmation modals
 * Targets modals with common naming patterns
 */
[id*="Modal"].modal:not(.show),
[id*="modal"].modal:not(.show),
[id*="confirmation"].modal:not(.show),
[id*="Confirmation"].modal:not(.show) {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
}

/* ==========================================================================
   Modal Backdrop FOUC Prevention
   ========================================================================== */

/**
 * Ensure modal backdrops are also hidden during page load
 */
.modal-backdrop {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
}

.modal-backdrop.show {
    display: block !important;
    opacity: 0.5 !important;
    visibility: visible !important;
}

/* ==========================================================================
   Page Load Performance Optimization
   ========================================================================== */

/**
 * Prevent any modal-related content from causing layout shifts
 * during initial page render
 */
.modal,
.modal-dialog,
.modal-content {
    will-change: auto;
}

/**
 * Ensure smooth transitions when modals do appear
 */
.modal.fade {
    transition: opacity 0.15s linear;
}

.modal.fade .modal-dialog {
    transition: transform 0.3s ease-out;
}

/* ==========================================================================
   Dark Mode Compatibility
   ========================================================================== */

/**
 * Ensure FOUC prevention works in both light and dark modes
 */
[data-theme-mode="dark"] .modal:not(.show),
[data-theme-mode="light"] .modal:not(.show) {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
}

/* ==========================================================================
   Responsive Considerations
   ========================================================================== */

/**
 * Ensure FOUC prevention works across all screen sizes
 */
@media (max-width: 576px) {
    .modal:not(.show) {
        display: none !important;
        opacity: 0 !important;
        visibility: hidden !important;
    }
}

@media (min-width: 577px) {
    .modal:not(.show) {
        display: none !important;
        opacity: 0 !important;
        visibility: hidden !important;
    }
}

/* ==========================================================================
   Browser Compatibility
   ========================================================================== */

/**
 * Additional vendor prefixes for older browsers
 */
.modal.fade.effect-scale .modal-dialog {
    -webkit-transform: scale(0.7);
    -moz-transform: scale(0.7);
    -ms-transform: scale(0.7);
    transform: scale(0.7);
    
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

.modal.fade.effect-scale.show .modal-dialog {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
}

/* ==========================================================================
   Debug Mode (Uncomment for troubleshooting)
   ========================================================================== */

/*
.modal {
    border: 2px solid red !important;
}

.modal.show {
    border: 2px solid green !important;
}
*/
